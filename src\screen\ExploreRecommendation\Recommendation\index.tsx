import { SortIcon } from '@/components/icons';
import RecommendationCard from './card';
import { recommendations } from '@/types/recommendationsData';


const Recommendation = () => {

  return (
    <div className=" relative">
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-containerbg">
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <p className="text-sm font-medium text-default-Secondary">Filter</p>
        </div>
      </div>

      {recommendations.map((item, index) => (
        <RecommendationCard
          key={item.title || `recommendation-${index}`}
          recommendations={item}
        />
      ))}
    </div>
  );
};

export default Recommendation;
